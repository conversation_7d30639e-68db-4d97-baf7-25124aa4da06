<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Adbar\\Dot' => $vendorDir . '/adbario/php-dot-notation/src/Dot.php',
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'Aura\\SqlQuery\\AbstractDmlQuery' => $vendorDir . '/aura/sqlquery/src/AbstractDmlQuery.php',
    'Aura\\SqlQuery\\AbstractQuery' => $vendorDir . '/aura/sqlquery/src/AbstractQuery.php',
    'Aura\\SqlQuery\\Common\\Delete' => $vendorDir . '/aura/sqlquery/src/Common/Delete.php',
    'Aura\\SqlQuery\\Common\\DeleteInterface' => $vendorDir . '/aura/sqlquery/src/Common/DeleteInterface.php',
    'Aura\\SqlQuery\\Common\\Insert' => $vendorDir . '/aura/sqlquery/src/Common/Insert.php',
    'Aura\\SqlQuery\\Common\\InsertInterface' => $vendorDir . '/aura/sqlquery/src/Common/InsertInterface.php',
    'Aura\\SqlQuery\\Common\\LimitInterface' => $vendorDir . '/aura/sqlquery/src/Common/LimitInterface.php',
    'Aura\\SqlQuery\\Common\\LimitOffsetInterface' => $vendorDir . '/aura/sqlquery/src/Common/LimitOffsetInterface.php',
    'Aura\\SqlQuery\\Common\\OrderByInterface' => $vendorDir . '/aura/sqlquery/src/Common/OrderByInterface.php',
    'Aura\\SqlQuery\\Common\\ReturningInterface' => $vendorDir . '/aura/sqlquery/src/Common/ReturningInterface.php',
    'Aura\\SqlQuery\\Common\\Select' => $vendorDir . '/aura/sqlquery/src/Common/Select.php',
    'Aura\\SqlQuery\\Common\\SelectInterface' => $vendorDir . '/aura/sqlquery/src/Common/SelectInterface.php',
    'Aura\\SqlQuery\\Common\\SubselectInterface' => $vendorDir . '/aura/sqlquery/src/Common/SubselectInterface.php',
    'Aura\\SqlQuery\\Common\\Update' => $vendorDir . '/aura/sqlquery/src/Common/Update.php',
    'Aura\\SqlQuery\\Common\\UpdateInterface' => $vendorDir . '/aura/sqlquery/src/Common/UpdateInterface.php',
    'Aura\\SqlQuery\\Common\\ValuesInterface' => $vendorDir . '/aura/sqlquery/src/Common/ValuesInterface.php',
    'Aura\\SqlQuery\\Common\\WhereInterface' => $vendorDir . '/aura/sqlquery/src/Common/WhereInterface.php',
    'Aura\\SqlQuery\\Exception' => $vendorDir . '/aura/sqlquery/src/Exception.php',
    'Aura\\SqlQuery\\Mysql\\Delete' => $vendorDir . '/aura/sqlquery/src/Mysql/Delete.php',
    'Aura\\SqlQuery\\Mysql\\Insert' => $vendorDir . '/aura/sqlquery/src/Mysql/Insert.php',
    'Aura\\SqlQuery\\Mysql\\Select' => $vendorDir . '/aura/sqlquery/src/Mysql/Select.php',
    'Aura\\SqlQuery\\Mysql\\Update' => $vendorDir . '/aura/sqlquery/src/Mysql/Update.php',
    'Aura\\SqlQuery\\Pgsql\\Delete' => $vendorDir . '/aura/sqlquery/src/Pgsql/Delete.php',
    'Aura\\SqlQuery\\Pgsql\\Insert' => $vendorDir . '/aura/sqlquery/src/Pgsql/Insert.php',
    'Aura\\SqlQuery\\Pgsql\\Select' => $vendorDir . '/aura/sqlquery/src/Pgsql/Select.php',
    'Aura\\SqlQuery\\Pgsql\\Update' => $vendorDir . '/aura/sqlquery/src/Pgsql/Update.php',
    'Aura\\SqlQuery\\QueryFactory' => $vendorDir . '/aura/sqlquery/src/QueryFactory.php',
    'Aura\\SqlQuery\\QueryInterface' => $vendorDir . '/aura/sqlquery/src/QueryInterface.php',
    'Aura\\SqlQuery\\Quoter' => $vendorDir . '/aura/sqlquery/src/Quoter.php',
    'Aura\\SqlQuery\\Sqlite\\Delete' => $vendorDir . '/aura/sqlquery/src/Sqlite/Delete.php',
    'Aura\\SqlQuery\\Sqlite\\Insert' => $vendorDir . '/aura/sqlquery/src/Sqlite/Insert.php',
    'Aura\\SqlQuery\\Sqlite\\Select' => $vendorDir . '/aura/sqlquery/src/Sqlite/Select.php',
    'Aura\\SqlQuery\\Sqlite\\Update' => $vendorDir . '/aura/sqlquery/src/Sqlite/Update.php',
    'Aura\\SqlQuery\\Sqlsrv\\Delete' => $vendorDir . '/aura/sqlquery/src/Sqlsrv/Delete.php',
    'Aura\\SqlQuery\\Sqlsrv\\Insert' => $vendorDir . '/aura/sqlquery/src/Sqlsrv/Insert.php',
    'Aura\\SqlQuery\\Sqlsrv\\Select' => $vendorDir . '/aura/sqlquery/src/Sqlsrv/Select.php',
    'Aura\\SqlQuery\\Sqlsrv\\Update' => $vendorDir . '/aura/sqlquery/src/Sqlsrv/Update.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Desarrolla2\\Cache\\AbstractCache' => $vendorDir . '/desarrolla2/cache/src/AbstractCache.php',
    'Desarrolla2\\Cache\\AbstractFile' => $vendorDir . '/desarrolla2/cache/src/AbstractFile.php',
    'Desarrolla2\\Cache\\Apcu' => $vendorDir . '/desarrolla2/cache/src/Apcu.php',
    'Desarrolla2\\Cache\\CacheInterface' => $vendorDir . '/desarrolla2/cache/src/CacheInterface.php',
    'Desarrolla2\\Cache\\Chain' => $vendorDir . '/desarrolla2/cache/src/Chain.php',
    'Desarrolla2\\Cache\\Exception\\BadMethodCallException' => $vendorDir . '/desarrolla2/cache/src/Exception/BadMethodCallException.php',
    'Desarrolla2\\Cache\\Exception\\CacheException' => $vendorDir . '/desarrolla2/cache/src/Exception/CacheException.php',
    'Desarrolla2\\Cache\\Exception\\InvalidArgumentException' => $vendorDir . '/desarrolla2/cache/src/Exception/InvalidArgumentException.php',
    'Desarrolla2\\Cache\\Exception\\UnexpectedValueException' => $vendorDir . '/desarrolla2/cache/src/Exception/UnexpectedValueException.php',
    'Desarrolla2\\Cache\\File' => $vendorDir . '/desarrolla2/cache/src/File.php',
    'Desarrolla2\\Cache\\File\\BasicFilename' => $vendorDir . '/desarrolla2/cache/src/File/BasicFilename.php',
    'Desarrolla2\\Cache\\File\\TrieFilename' => $vendorDir . '/desarrolla2/cache/src/File/TrieFilename.php',
    'Desarrolla2\\Cache\\Memcached' => $vendorDir . '/desarrolla2/cache/src/Memcached.php',
    'Desarrolla2\\Cache\\Memory' => $vendorDir . '/desarrolla2/cache/src/Memory.php',
    'Desarrolla2\\Cache\\MongoDB' => $vendorDir . '/desarrolla2/cache/src/MongoDB.php',
    'Desarrolla2\\Cache\\Mysqli' => $vendorDir . '/desarrolla2/cache/src/Mysqli.php',
    'Desarrolla2\\Cache\\NotCache' => $vendorDir . '/desarrolla2/cache/src/NotCache.php',
    'Desarrolla2\\Cache\\Option\\FilenameTrait' => $vendorDir . '/desarrolla2/cache/src/Option/FilenameTrait.php',
    'Desarrolla2\\Cache\\Option\\InitializeTrait' => $vendorDir . '/desarrolla2/cache/src/Option/InitializeTrait.php',
    'Desarrolla2\\Cache\\Option\\PrefixTrait' => $vendorDir . '/desarrolla2/cache/src/Option/PrefixTrait.php',
    'Desarrolla2\\Cache\\Option\\TtlTrait' => $vendorDir . '/desarrolla2/cache/src/Option/TtlTrait.php',
    'Desarrolla2\\Cache\\Packer\\JsonPacker' => $vendorDir . '/desarrolla2/cache/src/Packer/JsonPacker.php',
    'Desarrolla2\\Cache\\Packer\\MongoDBBinaryPacker' => $vendorDir . '/desarrolla2/cache/src/Packer/MongoDBBinaryPacker.php',
    'Desarrolla2\\Cache\\Packer\\NopPacker' => $vendorDir . '/desarrolla2/cache/src/Packer/NopPacker.php',
    'Desarrolla2\\Cache\\Packer\\PackerInterface' => $vendorDir . '/desarrolla2/cache/src/Packer/PackerInterface.php',
    'Desarrolla2\\Cache\\Packer\\PackingTrait' => $vendorDir . '/desarrolla2/cache/src/Packer/PackingTrait.php',
    'Desarrolla2\\Cache\\Packer\\SerializePacker' => $vendorDir . '/desarrolla2/cache/src/Packer/SerializePacker.php',
    'Desarrolla2\\Cache\\PhpFile' => $vendorDir . '/desarrolla2/cache/src/PhpFile.php',
    'Desarrolla2\\Cache\\Predis' => $vendorDir . '/desarrolla2/cache/src/Predis.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'Monolog\\Attribute\\AsMonologProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Attribute/AsMonologProcessor.php',
    'Monolog\\DateTimeImmutable' => $vendorDir . '/monolog/monolog/src/Monolog/DateTimeImmutable.php',
    'Monolog\\ErrorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/ErrorHandler.php',
    'Monolog\\Formatter\\ChromePHPFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ChromePHPFormatter.php',
    'Monolog\\Formatter\\ElasticaFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticaFormatter.php',
    'Monolog\\Formatter\\ElasticsearchFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ElasticsearchFormatter.php',
    'Monolog\\Formatter\\FlowdockFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FlowdockFormatter.php',
    'Monolog\\Formatter\\FluentdFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FluentdFormatter.php',
    'Monolog\\Formatter\\FormatterInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/FormatterInterface.php',
    'Monolog\\Formatter\\GelfMessageFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GelfMessageFormatter.php',
    'Monolog\\Formatter\\GoogleCloudLoggingFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/GoogleCloudLoggingFormatter.php',
    'Monolog\\Formatter\\HtmlFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/HtmlFormatter.php',
    'Monolog\\Formatter\\JsonFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/JsonFormatter.php',
    'Monolog\\Formatter\\LineFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LineFormatter.php',
    'Monolog\\Formatter\\LogglyFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogglyFormatter.php',
    'Monolog\\Formatter\\LogmaticFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogmaticFormatter.php',
    'Monolog\\Formatter\\LogstashFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/LogstashFormatter.php',
    'Monolog\\Formatter\\MongoDBFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/MongoDBFormatter.php',
    'Monolog\\Formatter\\NormalizerFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/NormalizerFormatter.php',
    'Monolog\\Formatter\\ScalarFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/ScalarFormatter.php',
    'Monolog\\Formatter\\WildfireFormatter' => $vendorDir . '/monolog/monolog/src/Monolog/Formatter/WildfireFormatter.php',
    'Monolog\\Handler\\AbstractHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractHandler.php',
    'Monolog\\Handler\\AbstractProcessingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractProcessingHandler.php',
    'Monolog\\Handler\\AbstractSyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AbstractSyslogHandler.php',
    'Monolog\\Handler\\AmqpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/AmqpHandler.php',
    'Monolog\\Handler\\BrowserConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BrowserConsoleHandler.php',
    'Monolog\\Handler\\BufferHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/BufferHandler.php',
    'Monolog\\Handler\\ChromePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ChromePHPHandler.php',
    'Monolog\\Handler\\CouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CouchDBHandler.php',
    'Monolog\\Handler\\CubeHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/CubeHandler.php',
    'Monolog\\Handler\\Curl\\Util' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Curl/Util.php',
    'Monolog\\Handler\\DeduplicationHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DeduplicationHandler.php',
    'Monolog\\Handler\\DoctrineCouchDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DoctrineCouchDBHandler.php',
    'Monolog\\Handler\\DynamoDbHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/DynamoDbHandler.php',
    'Monolog\\Handler\\ElasticaHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticaHandler.php',
    'Monolog\\Handler\\ElasticsearchHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ElasticsearchHandler.php',
    'Monolog\\Handler\\ErrorLogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ErrorLogHandler.php',
    'Monolog\\Handler\\FallbackGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FallbackGroupHandler.php',
    'Monolog\\Handler\\FilterHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FilterHandler.php',
    'Monolog\\Handler\\FingersCrossedHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossedHandler.php',
    'Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'Monolog\\Handler\\FirePHPHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FirePHPHandler.php',
    'Monolog\\Handler\\FleepHookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FleepHookHandler.php',
    'Monolog\\Handler\\FlowdockHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FlowdockHandler.php',
    'Monolog\\Handler\\FormattableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerInterface.php',
    'Monolog\\Handler\\FormattableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/FormattableHandlerTrait.php',
    'Monolog\\Handler\\GelfHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GelfHandler.php',
    'Monolog\\Handler\\GroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/GroupHandler.php',
    'Monolog\\Handler\\Handler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Handler.php',
    'Monolog\\Handler\\HandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerInterface.php',
    'Monolog\\Handler\\HandlerWrapper' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/HandlerWrapper.php',
    'Monolog\\Handler\\IFTTTHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/IFTTTHandler.php',
    'Monolog\\Handler\\InsightOpsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/InsightOpsHandler.php',
    'Monolog\\Handler\\LogEntriesHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogEntriesHandler.php',
    'Monolog\\Handler\\LogglyHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogglyHandler.php',
    'Monolog\\Handler\\LogmaticHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/LogmaticHandler.php',
    'Monolog\\Handler\\MailHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MailHandler.php',
    'Monolog\\Handler\\MandrillHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MandrillHandler.php',
    'Monolog\\Handler\\MissingExtensionException' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MissingExtensionException.php',
    'Monolog\\Handler\\MongoDBHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/MongoDBHandler.php',
    'Monolog\\Handler\\NativeMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NativeMailerHandler.php',
    'Monolog\\Handler\\NewRelicHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NewRelicHandler.php',
    'Monolog\\Handler\\NoopHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NoopHandler.php',
    'Monolog\\Handler\\NullHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/NullHandler.php',
    'Monolog\\Handler\\OverflowHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/OverflowHandler.php',
    'Monolog\\Handler\\PHPConsoleHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PHPConsoleHandler.php',
    'Monolog\\Handler\\ProcessHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessHandler.php',
    'Monolog\\Handler\\ProcessableHandlerInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerInterface.php',
    'Monolog\\Handler\\ProcessableHandlerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ProcessableHandlerTrait.php',
    'Monolog\\Handler\\PsrHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PsrHandler.php',
    'Monolog\\Handler\\PushoverHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/PushoverHandler.php',
    'Monolog\\Handler\\RedisHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisHandler.php',
    'Monolog\\Handler\\RedisPubSubHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RedisPubSubHandler.php',
    'Monolog\\Handler\\RollbarHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RollbarHandler.php',
    'Monolog\\Handler\\RotatingFileHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/RotatingFileHandler.php',
    'Monolog\\Handler\\SamplingHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SamplingHandler.php',
    'Monolog\\Handler\\SendGridHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SendGridHandler.php',
    'Monolog\\Handler\\SlackHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackHandler.php',
    'Monolog\\Handler\\SlackWebhookHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SlackWebhookHandler.php',
    'Monolog\\Handler\\Slack\\SlackRecord' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/Slack/SlackRecord.php',
    'Monolog\\Handler\\SocketHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SocketHandler.php',
    'Monolog\\Handler\\SqsHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SqsHandler.php',
    'Monolog\\Handler\\StreamHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/StreamHandler.php',
    'Monolog\\Handler\\SwiftMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SwiftMailerHandler.php',
    'Monolog\\Handler\\SymfonyMailerHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SymfonyMailerHandler.php',
    'Monolog\\Handler\\SyslogHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogHandler.php',
    'Monolog\\Handler\\SyslogUdpHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdpHandler.php',
    'Monolog\\Handler\\SyslogUdp\\UdpSocket' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'Monolog\\Handler\\TelegramBotHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TelegramBotHandler.php',
    'Monolog\\Handler\\TestHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/TestHandler.php',
    'Monolog\\Handler\\WebRequestRecognizerTrait' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WebRequestRecognizerTrait.php',
    'Monolog\\Handler\\WhatFailureGroupHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/WhatFailureGroupHandler.php',
    'Monolog\\Handler\\ZendMonitorHandler' => $vendorDir . '/monolog/monolog/src/Monolog/Handler/ZendMonitorHandler.php',
    'Monolog\\LogRecord' => $vendorDir . '/monolog/monolog/src/Monolog/LogRecord.php',
    'Monolog\\Logger' => $vendorDir . '/monolog/monolog/src/Monolog/Logger.php',
    'Monolog\\Processor\\GitProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/GitProcessor.php',
    'Monolog\\Processor\\HostnameProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/HostnameProcessor.php',
    'Monolog\\Processor\\IntrospectionProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/IntrospectionProcessor.php',
    'Monolog\\Processor\\MemoryPeakUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'Monolog\\Processor\\MemoryProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryProcessor.php',
    'Monolog\\Processor\\MemoryUsageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MemoryUsageProcessor.php',
    'Monolog\\Processor\\MercurialProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/MercurialProcessor.php',
    'Monolog\\Processor\\ProcessIdProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessIdProcessor.php',
    'Monolog\\Processor\\ProcessorInterface' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/ProcessorInterface.php',
    'Monolog\\Processor\\PsrLogMessageProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/PsrLogMessageProcessor.php',
    'Monolog\\Processor\\TagProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/TagProcessor.php',
    'Monolog\\Processor\\UidProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/UidProcessor.php',
    'Monolog\\Processor\\WebProcessor' => $vendorDir . '/monolog/monolog/src/Monolog/Processor/WebProcessor.php',
    'Monolog\\Registry' => $vendorDir . '/monolog/monolog/src/Monolog/Registry.php',
    'Monolog\\ResettableInterface' => $vendorDir . '/monolog/monolog/src/Monolog/ResettableInterface.php',
    'Monolog\\SignalHandler' => $vendorDir . '/monolog/monolog/src/Monolog/SignalHandler.php',
    'Monolog\\Test\\TestCase' => $vendorDir . '/monolog/monolog/src/Monolog/Test/TestCase.php',
    'Monolog\\Utils' => $vendorDir . '/monolog/monolog/src/Monolog/Utils.php',
    'Parsedown' => $vendorDir . '/erusev/parsedown/Parsedown.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\Log\\AbstractLogger' => $vendorDir . '/psr/log/Psr/Log/AbstractLogger.php',
    'Psr\\Log\\InvalidArgumentException' => $vendorDir . '/psr/log/Psr/Log/InvalidArgumentException.php',
    'Psr\\Log\\LogLevel' => $vendorDir . '/psr/log/Psr/Log/LogLevel.php',
    'Psr\\Log\\LoggerAwareInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareInterface.php',
    'Psr\\Log\\LoggerAwareTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerAwareTrait.php',
    'Psr\\Log\\LoggerInterface' => $vendorDir . '/psr/log/Psr/Log/LoggerInterface.php',
    'Psr\\Log\\LoggerTrait' => $vendorDir . '/psr/log/Psr/Log/LoggerTrait.php',
    'Psr\\Log\\NullLogger' => $vendorDir . '/psr/log/Psr/Log/NullLogger.php',
    'Psr\\Log\\Test\\DummyTest' => $vendorDir . '/psr/log/Psr/Log/Test/DummyTest.php',
    'Psr\\Log\\Test\\LoggerInterfaceTest' => $vendorDir . '/psr/log/Psr/Log/Test/LoggerInterfaceTest.php',
    'Psr\\Log\\Test\\TestLogger' => $vendorDir . '/psr/log/Psr/Log/Test/TestLogger.php',
    'Psr\\SimpleCache\\CacheException' => $vendorDir . '/psr/simple-cache/src/CacheException.php',
    'Psr\\SimpleCache\\CacheInterface' => $vendorDir . '/psr/simple-cache/src/CacheInterface.php',
    'Psr\\SimpleCache\\InvalidArgumentException' => $vendorDir . '/psr/simple-cache/src/InvalidArgumentException.php',
    'StoreKeeper\\ApiWrapperDev\\DebugApiWrapper' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DebugApiWrapper.php',
    'StoreKeeper\\ApiWrapperDev\\DumpFile' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DumpFile.php',
    'StoreKeeper\\ApiWrapperDev\\DumpFile\\Context' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DumpFile/Context.php',
    'StoreKeeper\\ApiWrapperDev\\DumpFile\\Reader' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DumpFile/Reader.php',
    'StoreKeeper\\ApiWrapperDev\\DumpFile\\TypeDependentTrait' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DumpFile/TypeDependentTrait.php',
    'StoreKeeper\\ApiWrapperDev\\DumpFile\\Writer' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/DumpFile/Writer.php',
    'StoreKeeper\\ApiWrapperDev\\MockAction' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/MockAction.php',
    'StoreKeeper\\ApiWrapperDev\\MockModuleApiWrapperFactory' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/MockModuleApiWrapperFactory.php',
    'StoreKeeper\\ApiWrapperDev\\TestEnvLoader' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/TestEnvLoader.php',
    'StoreKeeper\\ApiWrapperDev\\Wrapper\\MockAdapter' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapperDev/Wrapper/MockAdapter.php',
    'StoreKeeper\\ApiWrapper\\ActionWrapper' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ActionWrapper.php',
    'StoreKeeper\\ApiWrapper\\ActionWrapperInterface' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ActionWrapperInterface.php',
    'StoreKeeper\\ApiWrapper\\ApiWrapper' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ApiWrapper.php',
    'StoreKeeper\\ApiWrapper\\ApiWrapperInterface' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ApiWrapperInterface.php',
    'StoreKeeper\\ApiWrapper\\Auth' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Auth.php',
    'StoreKeeper\\ApiWrapper\\Auth\\AnonymousAuth' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Auth/AnonymousAuth.php',
    'StoreKeeper\\ApiWrapper\\Exception\\AuthException' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Exception/AuthException.php',
    'StoreKeeper\\ApiWrapper\\Exception\\ConnectionException' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Exception/ConnectionException.php',
    'StoreKeeper\\ApiWrapper\\Exception\\GeneralException' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Exception/GeneralException.php',
    'StoreKeeper\\ApiWrapper\\Exception\\InternalException' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Exception/InternalException.php',
    'StoreKeeper\\ApiWrapper\\Exception\\LookupException' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Exception/LookupException.php',
    'StoreKeeper\\ApiWrapper\\Iterator\\ListCallByIdIterator' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Iterator/ListCallByIdIterator.php',
    'StoreKeeper\\ApiWrapper\\Iterator\\ListCallByIdPaginatedIterator' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Iterator/ListCallByIdPaginatedIterator.php',
    'StoreKeeper\\ApiWrapper\\Iterator\\ListCallIterator' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Iterator/ListCallIterator.php',
    'StoreKeeper\\ApiWrapper\\Iterator\\ListCallPaginatedIterator' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Iterator/ListCallPaginatedIterator.php',
    'StoreKeeper\\ApiWrapper\\Iterator\\PaginatedIteratorTrait' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Iterator/PaginatedIteratorTrait.php',
    'StoreKeeper\\ApiWrapper\\ModuleApiWrapper' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ModuleApiWrapper.php',
    'StoreKeeper\\ApiWrapper\\ModuleApiWrapperInterface' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/ModuleApiWrapperInterface.php',
    'StoreKeeper\\ApiWrapper\\Wrapper\\AsyncFullJsonAdapter' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Wrapper/AsyncFullJsonAdapter.php',
    'StoreKeeper\\ApiWrapper\\Wrapper\\FullJsonAdapter' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Wrapper/FullJsonAdapter.php',
    'StoreKeeper\\ApiWrapper\\Wrapper\\SwooleFullJsonAdapter' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Wrapper/SwooleFullJsonAdapter.php',
    'StoreKeeper\\ApiWrapper\\Wrapper\\WrapperInterface' => $vendorDir . '/storekeeper/api-wrapper/src/StoreKeeper/ApiWrapper/Wrapper/WrapperInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Activator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Activator.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\BackofficeCore' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/BackofficeCore.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Helpers\\OverlayRenderer' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Helpers\\ProductXEditor' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/ProductXEditor.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Helpers\\TableRenderer' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/TableRenderer.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\MenuStructure' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\MetaBoxes\\AbstractPostSyncMetaBox' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/AbstractPostSyncMetaBox.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\MetaBoxes\\OrderSyncMetaBox' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\MetaBoxes\\ProductSyncMetaBox' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Notices\\AdminNotices' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\AbstractPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\AbstractPageLike' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPageLike.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\AbstractTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\DashboardPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/DashboardPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\FormElementTrait' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/FormElementTrait.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\LogsPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/LogsPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\SettingsPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\StatusPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StatusPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\StoreKeeperSeoPages' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\AbstractLogsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/AbstractLogsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\BackofficeRolesTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\ConnectionTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\DashboardTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DashboardTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\DeveloperSettingsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DeveloperSettingsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\ExportSettingsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\ExportTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\FrontendSettingsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\LogPurgerTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\PluginConflictCheckerTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/PluginConflictCheckerTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\SchedulerTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\SkuLogFormatter' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\StatusTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\SynCheckTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\TaskLogsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\Tabs\\WebhookLogsTab' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php',
    'StoreKeeper\\WooCommerce\\B2C\\Backoffice\\Pages\\ToolsPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Cache\\AbstractCache' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Cache/AbstractCache.php',
    'StoreKeeper\\WooCommerce\\B2C\\Cache\\ShopProductCache' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Cache/ShopProductCache.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\AbstractCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/AbstractCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\AbstractMarkTasksAs' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/AbstractMarkTasksAs.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\AbstractSyncCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/AbstractSyncCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\CleanWoocommerceEnvironment' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\CommandInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/CommandInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\CommandRunner' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/CommandRunner.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ConnectBackend' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ConnectBackend.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\AbstractFileExportCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/AbstractFileExportCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportAll' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAll.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportAttribute' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttribute.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportAttributeOption' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttributeOption.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportCategory' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCategory.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportCustomer' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCustomer.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportProduct' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProduct.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportProductBlueprint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProductBlueprint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\FileExports\\FileExportTag' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportTag.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\MarkTasksAsRetry' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsRetry.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\MarkTasksAsSuccess' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsSuccess.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\AbstractModelCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\AbstractModelDeleteCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelDeleteCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\AbstractModelGetCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelGetCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\AbstractModelListCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelListCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\AbstractModelPurgeCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelPurgeCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\Task\\TaskDelete' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskDelete.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\Task\\TaskGet' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskGet.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\Task\\TaskList' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskList.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\Task\\TaskPurge' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurge.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\Task\\TaskPurgeOld' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurgeOld.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\WebhookLog\\WebhookLogDelete' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogDelete.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\WebhookLog\\WebhookLogGet' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogGet.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\WebhookLog\\WebhookLogList' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogList.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\WebhookLog\\WebhookLogPurge' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurge.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ModelCommands\\WebhookLog\\WebhookLogPurgeOld' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurgeOld.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\PaymentGatewaySettings' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/PaymentGatewaySettings.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ProcessAllTasks' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ProcessSingleTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ProcessSingleTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\ScheduledProcessor' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/ScheduledProcessor.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceAttributeOptionPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceAttributeOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceAttributes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceCategories' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCategories.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceCouponCodes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCouponCodes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceCrossSellProductPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceCrossSellProducts' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProducts.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceFeaturedAttributes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFeaturedAttributes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceFullSync' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceProductPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceProducts' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProducts.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceShippingMethods' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShippingMethods.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceShopInfo' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShopInfo.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceSingleProduct' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceSingleProduct.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceTags' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceTags.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceUpsellProductPage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\SyncWoocommerceUpsellProducts' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProducts.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\WebCommandRunner' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/WebCommandRunner.php',
    'StoreKeeper\\WooCommerce\\B2C\\Commands\\WpCliCommandRunner' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Commands/WpCliCommandRunner.php',
    'StoreKeeper\\WooCommerce\\B2C\\Core' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Core.php',
    'StoreKeeper\\WooCommerce\\B2C\\Cron' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Cron.php',
    'StoreKeeper\\WooCommerce\\B2C\\Cron\\CronRegistrar' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php',
    'StoreKeeper\\WooCommerce\\B2C\\Cron\\ProcessTaskCron' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Cron/ProcessTaskCron.php',
    'StoreKeeper\\WooCommerce\\B2C\\Database\\DatabaseConnection' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Database/DatabaseConnection.php',
    'StoreKeeper\\WooCommerce\\B2C\\Database\\MySqlLock' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Database/MySqlLock.php',
    'StoreKeeper\\WooCommerce\\B2C\\Deactivator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Deactivator.php',
    'StoreKeeper\\WooCommerce\\B2C\\Debug\\HookDumpFile' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Debug/HookDumpFile.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\AbstractEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/AbstractEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\EndpointLoader' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/EndpointLoader.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\FileExport\\ExportEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/FileExport/ExportEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Sso\\SsoGetEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Sso/SsoGetEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\TaskProcessor\\TaskProcessorEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/TaskProcessor/TaskProcessorEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\WebService\\AddressSearchEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/WebService/AddressSearchEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\DisconnectHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/DisconnectHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\EventsHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/EventsHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\InfoEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/InfoEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\InfoHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/InfoHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\InitHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/InitHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\SsoHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/SsoHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Endpoints\\Webhooks\\WebhookPostEndpoint' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Endpoints/Webhooks/WebhookPostEndpoint.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\AccessDeniedException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/AccessDeniedException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\AttributeTranslatorException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/AttributeTranslatorException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\BaseException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/BaseException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\BootError' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/BootError.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\CannotFetchShopProductException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/CannotFetchShopProductException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\ConnectionTimedOutException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/ConnectionTimedOutException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\CronRunnerException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/CronRunnerException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\EmailIsAdminUserException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/EmailIsAdminUserException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\ExportException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/ExportException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\FileExportFailedException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/FileExportFailedException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\InvalidRunnerException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/InvalidRunnerException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\LockActiveException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/LockActiveException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\LockException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/LockException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\LockTimeoutException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/LockTimeoutException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\LogDirectoryUnwritableException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/LogDirectoryUnwritableException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\MigrationExeption' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/MigrationExeption.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\NoLoggerException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/NoLoggerException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\NonExistentObjectException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/NonExistentObjectException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\NotConnectedException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/NotConnectedException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\OrderDifferenceException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/OrderDifferenceException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\PaymentException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/PaymentException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\PluginDisconnectedException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/PluginDisconnectedException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\ProductImportException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/ProductImportException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\ProductSkuEmptyException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/ProductSkuEmptyException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\ShippingMethodImportException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/ShippingMethodImportException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\SpreadSheetLineDataException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/SpreadSheetLineDataException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\SpreadSheetWriterException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/SpreadSheetWriterException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\SqlException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/SqlException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\SsoAuthException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/SsoAuthException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\SubProcessException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/SubProcessException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\TableNeedsInnoDbException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/TableNeedsInnoDbException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\TableOperationSqlException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/TableOperationSqlException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\UnsupportedShippingMethodTypeException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/UnsupportedShippingMethodTypeException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\WordpressException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/WordpressException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\WpCliException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/WpCliException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exceptions\\WpRestException' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exceptions/WpRestException.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exports\\AbstractExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exports/AbstractExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exports\\OrderExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exports/OrderExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Exports\\OrderRefundExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Exports/OrderRefundExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Factories\\LoggerFactory' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Factories/LoggerFactory.php',
    'StoreKeeper\\WooCommerce\\B2C\\Factories\\WpAdminFormatter' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Factories/WpAdminFormatter.php',
    'StoreKeeper\\WooCommerce\\B2C\\Factories\\WpAdminHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Factories/WpAdminHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Factories\\WpCliHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Factories/WpCliHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\AbstractCSVFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/AbstractCSVFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\AbstractFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/AbstractFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\AllFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/AllFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\AttributeFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/AttributeFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\AttributeOptionsFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/AttributeOptionsFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\CategoryFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/CategoryFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\CustomerFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/CustomerFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\ProductBlueprintFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/ProductBlueprintFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\ProductFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/ProductFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\FileExport\\TagFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/FileExport/TagFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Filters\\OrderTrackingMessage' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Filters/OrderTrackingMessage.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Filters\\PrepareProductCategorySummaryFilter' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Filters/PrepareProductCategorySummaryFilter.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\FrontendCore' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/FrontendCore.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\AddressFormHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\AddressFormattingHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormattingHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\CategorySummaryHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/CategorySummaryHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\CustomerEmailHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/CustomerEmailHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\CustomerLoginRegisterHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/CustomerLoginRegisterHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\MarkdownHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/MarkdownHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\OrderHookHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/OrderHookHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\ProductAddOnHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\Seo' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/Seo.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\StoreKeeperSeoHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/StoreKeeperSeoHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\Handlers\\SubscribeHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/SubscribeHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\ShortCodes\\AbstractShortCode' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/AbstractShortCode.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\ShortCodes\\FormShortCode' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/FormShortCode.php',
    'StoreKeeper\\WooCommerce\\B2C\\Frontend\\ShortCodes\\MarkdownCode' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/MarkdownCode.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\DateTimeHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\FileExportTypeHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\HtmlEscape' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/HtmlEscape.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\PluginConflictChecker' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/PluginConflictChecker.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\ProductHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/ProductHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\ProductSkuGenerator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\RoleHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/RoleHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\Seo\\RankMathSeo' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/Seo/RankMathSeo.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\Seo\\StoreKeeperSeo' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/Seo/StoreKeeperSeo.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\Seo\\YoastSeo' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/Seo/YoastSeo.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\ServerStatusChecker' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\SsoHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/SsoHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Helpers\\WpCliHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Helpers/WpCliHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Hooks\\AbstractWpFilter' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Hooks/AbstractWpFilter.php',
    'StoreKeeper\\WooCommerce\\B2C\\Hooks\\WithHooksInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Hooks/WithHooksInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Hooks\\WpFilterInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Hooks/WpFilterInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\I18N' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/I18N.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\AbstractImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/AbstractImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\AbstractProductImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/AbstractProductImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\AttributeImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/AttributeImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\AttributeOptionImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/AttributeOptionImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\CategoryImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/CategoryImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\CouponCodeImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/CouponCodeImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\FeaturedAttributeImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/FeaturedAttributeImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\FullProductImportWithSelectiveIds' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/FullProductImportWithSelectiveIds.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\MenuItemImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/MenuItemImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\OrderImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/OrderImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\ProductImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/ProductImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\ProductParentImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/ProductParentImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\ProductStockImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/ProductStockImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\ProductUpdateImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/ProductUpdateImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\RedirectImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/RedirectImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\ShippingMethodImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/ShippingMethodImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Imports\\TagImport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Imports/TagImport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\IFileExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/IFileExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\IFileExportCommand' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/IFileExportCommand.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\IFileExportSpreadSheet' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/IFileExportSpreadSheet.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\IModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/IModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\IModelPurge' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/IModelPurge.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\LockInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/LockInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\ProductExportInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/ProductExportInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\TagExportInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/TagExportInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Interfaces\\WithConsoleProgressBarInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Interfaces/WithConsoleProgressBarInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Loggers\\WpCLILogger' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Loggers/WpCLILogger.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\AbstractMigration' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/AbstractMigration.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\AbstractTaskRetryMigration' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/AbstractTaskRetryMigration.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\AllVersions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/AllVersions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\MigrationManager' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/MigrationManager.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\VersionsInterface' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/VersionsInterface.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154000webhook' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154000webhook.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154010task' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154010task.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154020attribute' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154020attribute.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154030attributeOption' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154030attributeOption.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154040Payment' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154040Payment.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230312154050Refund' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230312154050Refund.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230313161100RedirectTable' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230313161100RedirectTable.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230313161110SetUpOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230313161110SetUpOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230313171650AttributeFkEnsure' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230313171650AttributeFkEnsure.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230313171651AttributeOptionFkAttributeEnsure' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230313171651AttributeOptionFkAttributeEnsure.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230313171652AttributeOptionFkTermEnsure' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230313171652AttributeOptionFkTermEnsure.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230319110710DropPkPayments' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230319110710DropPkPayments.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230319110720PaymentsAddPkey' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230319110720PaymentsAddPkey.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230319114000PaymentsAddTrx' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230319114000PaymentsAddTrx.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20230319124000PaymentsAddIsPaid' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20230319124000PaymentsAddIsPaid.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20231010192200CreateWebshopManagerRole' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20231010192200CreateWebshopManagerRole.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20231110095200TaskIndexTimesRan' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20231110095200TaskIndexTimesRan.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20231126172100ShippingZones' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20231126172100ShippingZones.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20231204152300ShippingMethods' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20231204152300ShippingMethods.php',
    'StoreKeeper\\WooCommerce\\B2C\\Migrations\\Versions\\V20240307192301RetryTasks' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Migrations/Versions/V20240307192301RetryTasks.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\AbstractModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/AbstractModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\AttributeModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/AttributeModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\AttributeModel\\MigrateFromOldData' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/AttributeModel/MigrateFromOldData.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\AttributeOptionModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/AttributeOptionModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\MigrationVersionModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/MigrationVersionModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\PaymentModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/PaymentModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\RefundModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/RefundModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\ShippingMethodModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/ShippingMethodModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\ShippingZoneModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/ShippingZoneModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\TaskModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/TaskModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Models\\WebhookLogModel' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Models/WebhookLogModel.php',
    'StoreKeeper\\WooCommerce\\B2C\\Objects\\PluginStatus' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Objects/PluginStatus.php',
    'StoreKeeper\\WooCommerce\\B2C\\Objects\\ShopCustomer' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Objects/ShopCustomer.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\AbstractOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/AbstractOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\CronOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/CronOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\FeaturedAttributeExportOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/FeaturedAttributeExportOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\FeaturedAttributeOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/FeaturedAttributeOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\StoreKeeperOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/StoreKeeperOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Options\\WooCommerceOptions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Options/WooCommerceOptions.php',
    'StoreKeeper\\WooCommerce\\B2C\\PaymentGateway\\BlockSupport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/PaymentGateway/BlockSupport.php',
    'StoreKeeper\\WooCommerce\\B2C\\PaymentGateway\\PaymentGateway' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php',
    'StoreKeeper\\WooCommerce\\B2C\\PaymentGateway\\StoreKeeperBaseGateway' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/PaymentGateway/StoreKeeperBaseGateway.php',
    'StoreKeeper\\WooCommerce\\B2C\\Query\\CronQueryBuilder' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Query/CronQueryBuilder.php',
    'StoreKeeper\\WooCommerce\\B2C\\Query\\OptionQueryBuilder' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Query/OptionQueryBuilder.php',
    'StoreKeeper\\WooCommerce\\B2C\\Query\\ProductQueryBuilder' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Query/ProductQueryBuilder.php',
    'StoreKeeper\\WooCommerce\\B2C\\Singletons\\QueryFactorySingleton' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Singletons/QueryFactorySingleton.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\AbstractProductTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/AbstractProductTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\AbstractTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/AbstractTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\AttributeImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/AttributeImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\AttributeOptionDelete' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/AttributeOptionDelete.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\AttributeOptionImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/AttributeOptionImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\CategoryDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/CategoryDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\CategoryImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/CategoryImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\CouponCodeDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/CouponCodeDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\CouponCodeImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/CouponCodeImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\MenuItemDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/MenuItemDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\MenuItemImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/MenuItemImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\OrderDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/OrderDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\OrderExportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/OrderExportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\OrderImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/OrderImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\OrderPaymentStatusUpdateTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/OrderPaymentStatusUpdateTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\OrderRefundTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/OrderRefundTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ParentProductRecalculationTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ParentProductRecalculationTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductActivateTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductActivateTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductDeactivateTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductDeactivateTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductStockUpdateTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductStockUpdateTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ProductUpdateImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ProductUpdateImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\RedirectDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/RedirectDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\RedirectImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/RedirectImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ShippingMethodDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ShippingMethodDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\ShippingMethodImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/ShippingMethodImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\TagDeleteTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/TagDeleteTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\TagImportTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/TagImportTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tasks\\TriggerVariationSaveActionTask' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tasks/TriggerVariationSaveActionTask.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\ActionFilterLoader' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/ActionFilterLoader.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Attributes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Attributes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Base36Coder' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Base36Coder.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Categories' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Categories.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\CommonAttributeName' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/CommonAttributeName.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\CommonAttributeOptionName' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/CommonAttributeOptionName.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\CustomerFinder' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/CustomerFinder.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\EndpointRequestValidator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/EndpointRequestValidator.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Export\\AttributeExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Export/AttributeExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Export\\BlueprintExport' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Export/BlueprintExport.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\FeaturedAttributes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\IniHelper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/IniHelper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Language' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Language.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Loader' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Loader.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\Media' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/Media.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\OrderHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/OrderHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\ParseDown' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/ParseDown.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\ProductAttributes' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/ProductAttributes.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\RedirectHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/RedirectHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\StoreKeeperApi' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/StoreKeeperApi.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\StringFunctions' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/StringFunctions.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\TaskHandler' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\TaskMarkingTrait' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/TaskMarkingTrait.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\TaskRateCalculator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/TaskRateCalculator.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\WordpressCleaner' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/WordpressCleaner.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\WordpressExceptionThrower' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/WordpressExceptionThrower.php',
    'StoreKeeper\\WooCommerce\\B2C\\Tools\\WordpressRestRequestWrapper' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Tools/WordpressRestRequestWrapper.php',
    'StoreKeeper\\WooCommerce\\B2C\\Traits\\ConsoleProgressBarTrait' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Traits/ConsoleProgressBarTrait.php',
    'StoreKeeper\\WooCommerce\\B2C\\Traits\\TaskHandlerTrait' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Traits/TaskHandlerTrait.php',
    'StoreKeeper\\WooCommerce\\B2C\\Updator' => $baseDir . '/src/StoreKeeper/WooCommerce/B2C/Updator.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'Symfony\\Component\\Process\\Exception\\ExceptionInterface' => $vendorDir . '/symfony/process/Exception/ExceptionInterface.php',
    'Symfony\\Component\\Process\\Exception\\InvalidArgumentException' => $vendorDir . '/symfony/process/Exception/InvalidArgumentException.php',
    'Symfony\\Component\\Process\\Exception\\LogicException' => $vendorDir . '/symfony/process/Exception/LogicException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessFailedException' => $vendorDir . '/symfony/process/Exception/ProcessFailedException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessSignaledException' => $vendorDir . '/symfony/process/Exception/ProcessSignaledException.php',
    'Symfony\\Component\\Process\\Exception\\ProcessTimedOutException' => $vendorDir . '/symfony/process/Exception/ProcessTimedOutException.php',
    'Symfony\\Component\\Process\\Exception\\RuntimeException' => $vendorDir . '/symfony/process/Exception/RuntimeException.php',
    'Symfony\\Component\\Process\\ExecutableFinder' => $vendorDir . '/symfony/process/ExecutableFinder.php',
    'Symfony\\Component\\Process\\InputStream' => $vendorDir . '/symfony/process/InputStream.php',
    'Symfony\\Component\\Process\\PhpExecutableFinder' => $vendorDir . '/symfony/process/PhpExecutableFinder.php',
    'Symfony\\Component\\Process\\PhpProcess' => $vendorDir . '/symfony/process/PhpProcess.php',
    'Symfony\\Component\\Process\\Pipes\\AbstractPipes' => $vendorDir . '/symfony/process/Pipes/AbstractPipes.php',
    'Symfony\\Component\\Process\\Pipes\\PipesInterface' => $vendorDir . '/symfony/process/Pipes/PipesInterface.php',
    'Symfony\\Component\\Process\\Pipes\\UnixPipes' => $vendorDir . '/symfony/process/Pipes/UnixPipes.php',
    'Symfony\\Component\\Process\\Pipes\\WindowsPipes' => $vendorDir . '/symfony/process/Pipes/WindowsPipes.php',
    'Symfony\\Component\\Process\\Process' => $vendorDir . '/symfony/process/Process.php',
    'Symfony\\Component\\Process\\ProcessUtils' => $vendorDir . '/symfony/process/ProcessUtils.php',
    'Symfony\\Polyfill\\Php80\\Php80' => $vendorDir . '/symfony/polyfill-php80/Php80.php',
    'Symfony\\Polyfill\\Php80\\PhpToken' => $vendorDir . '/symfony/polyfill-php80/PhpToken.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
