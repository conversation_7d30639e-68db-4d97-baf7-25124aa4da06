# Copyright (C) 2024 StoreKeeper for WooCommerce
# This file is distributed under the same license as the StoreKeeper for WooCommerce package.
msgid ""
msgstr ""
"Project-Id-Version: StoreKeeper for WooCommerce 0.0.1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/storekeeper-for-"
"woocommerce\n"
"POT-Creation-Date: 2024-12-26 10:43:24+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:116
msgid "Your allowed memory size has been exhausted."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:119
msgid "Current memory limit configured: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:125
msgid "Below are actions you can do to increase PHP memory limit:"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:153
msgid "You can increase the memory by adding %s on the wp-config."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:157
msgid "Suggested memory limit is 1G and then do a full sync."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:164
msgid "You can try executing this command via ssh."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:167
msgid "Go to the public_html folder of your webshop: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:171
msgid "Execute %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:179
msgid "Contact your hosting provider."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Helpers/OverlayRenderer.php:181
msgid ""
"If you are not comfortable in trying the methods above, or it did not work "
"for you. You can talk to your hosting provider about having them increase "
"your memory limit."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:53
msgid "Woocommerce Builder cannot be loaded."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:54
msgid " It was tested with version builder version: %s. "
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:65
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:66
msgid "StoreKeeper"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:92
msgid "Webshop Builder"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:107
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/DashboardPage.php:13
msgid "Dashboard"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:113
msgid "Logs"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:117
msgid "Tools"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:121
msgid "Settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MenuStructure.php:125
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StatusPage.php:13
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:204
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:127
msgid "Status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:26
msgid "Order sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:51
msgid "%s sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:73
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:41
msgid "Backoffice ID"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:76
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:44
msgid "Last sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:84
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:52
msgid "Open in backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:91
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:59
msgid "Force sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:95
msgid ""
"All orders won't be synced automatically with your currently selected "
"Synchronization mode, but you can do it manually using the Force sync button"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:143
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:168
msgid "Failed to sync order"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:143
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:136
msgid "Please try again"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/OrderSyncMetaBox.php:180
msgid "Order was synced successfully."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:26
msgid "Product synchronization"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:120
msgid "Something went wrong while syncing product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:127
msgid "Product was synced successfully."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/MetaBoxes/ProductSyncMetaBox.php:136
msgid "Failed to sync product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:38
msgid "Failed to build notices: %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:49
msgid "New products are not synced back to StoreKeeper."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:59
msgid ""
"The log directory cannot be created and is need by plugin to work properly. "
"Please create \"%s\" directory with writeable permissions or contact your "
"server provider."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:80
msgid ""
"The StoreKeeper synchronization plugin extension is not connected, thus "
"nothing is synchronized. please click configure to do so."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:84
msgid "Configure StoreKeeper synchronization plugin"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:108
msgid "product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:118
msgid "category"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:128
msgid "tag"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:143
msgid "attribute"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:152
msgid "attribute term"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:169
msgid ""
"This order is marked as completed, any changes might not be synced to the "
"backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:193
msgid ""
"This %s is synced from the backoffice, thus any changes won't be synced back "
"and can be overwritten when made in WooCommerce!"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Notices/AdminNotices.php:271
msgid ""
"It seems that its been %s ago since the process tasks cron has been running, "
"Contact your system administrator to solve this problem."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:75
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:67
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:83
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:181
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:197
msgid "PHP %s extension"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:82
msgid "The following required extensions are missing from your server: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:85
msgid ""
"Contact your server provider to enable these extensions for the StoreKeeper "
"synchronization plugin to function properly."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:128
msgid "StoreKeeper Sync Plugin"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:174
msgid "No tabs set for this page"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/AbstractPage.php:180
msgid "Failed to render the tab"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/LogsPage.php:20
msgid "Task error details"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/LogsPage.php:36
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:54
msgid "Tasks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/LogsPage.php:39
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:94
msgid "Webhooks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php:18
msgid "Connection"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php:19
msgid "Frontend settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php:20
#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:100
msgid "Scheduler settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php:22
msgid "Developer settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/SettingsPage.php:24
msgid "Backoffice roles"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:32
msgid "StoreKeeper Seo"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:41
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:56
msgid "Seo title"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:42
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:62
msgid "Seo keywords"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:43
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:69
msgid "Seo description"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:109
msgid ""
"This is managed product, you can only edit this seo information in "
"StoreKeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:115
msgid "Title"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:125
msgid "Keywords"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/StoreKeeperSeoPages.php:135
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:196
msgid "Description"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/AbstractLogsTab.php:80
msgid "results"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/AbstractLogsTab.php:84
msgid "%s of %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/AbstractLogsTab.php:113
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:117
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:40
msgid "Date"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php:43
msgid "Apply to all"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php:49
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:191
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php:66
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:38
msgid "Save settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php:138
msgid "Disable SSO"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php:146
msgid "Other backoffice roles"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/BackofficeRolesTab.php:150
msgid "Backoffice %s role"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:69
msgid ""
"Shipping method synchronization is not supported, please upgrade to a higher "
"package."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:80
msgid "Currently connected to"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:83
msgid "Disconnect"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:98
msgid "Backoffice connection"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:101
msgid "Connect to StoreKeeper"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:110
msgid "Backoffice API key"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:114
msgid "Steps"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:118
msgid "Copy the \"Backoffice API Key\" from the text area."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:119
msgid "Log into your admin environment"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:120
msgid ""
"Navigate to settings > technical settings and click on the \"webhook\" tab."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:121
msgid ""
"Paste your api key into the field that says \"Api key\" and click connect."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:122
msgid "Once done, you should reload this page and you will be fully connected."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:140
msgid "Sync statistics"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:143
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:73
msgid "Tasks in queue"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:146
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:76
msgid " (new: %s p/h, processing rate: %s p/h)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:156
msgid "Webhooks log count"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:161
msgid "Last task processed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:166
msgid "Last webhook received"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:178
msgid "Synchronization settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:209
msgid "No tasks processed (successfully) yet, but the cron is running"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:212
msgid "Never, please check the \"cron tab\" notice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:219
msgid "Never"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:372
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:83
msgid "Full sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:373
msgid "Order only"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:374
msgid "Products only"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:375
msgid "No sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:377
msgid ""
"Products, categories, labels/tags, attributes with options, coupons, orders "
"and customers are being synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:381
msgid ""
"Orders and customers are being exported, order status and product stock with "
"matching skus are being imports"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:385
msgid ""
"Products, categories, labels/tags, attributes with options, and coupons are "
"being synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:389
msgid "Nothing will be synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:409
msgid "Synchronization mode"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:425
msgid "Storekeeper SEO handler"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:426
msgid "Don't handle SEO"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:430
msgid "Yoast SEO handler"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:434
msgid "Rank Math SEO handler"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:438
msgid "SEO handler"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:452
msgid "Barcode meta key"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:460
msgid ""
"Changing this settings allows to use various EAN, barcode plugins. After "
"changing this setting all products need to be synchronized again."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:472
msgid "Order sync from date"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:476
msgid ""
"Order created before the date set will not be synchronized to backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:488
msgid "Payments are disabled in currently selected Synchronization mode"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:491
msgid "Activate StoreKeeper payments"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:496
msgid ""
"When checked, active webshop payment methods from your StoreKeeper "
"backoffice are added to your webshop's checkout"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:508
msgid ""
"Shipping methods are disabled in currently selected Synchronization mode"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:511
msgid "Use StoreKeeper shipping methods"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:516
msgid ""
"When checked, shipping countries and methods from your StoreKeeper "
"backoffice are added to your WooCommerce settings and webshop's checkout"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:527
msgid "Import category description as HTML"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:531
msgid ""
"It will import the category descriptions as html, otherwise plain text. It "
"requires a theme support for rendering it correctly."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:542
msgid "Notify when backorderable"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ConnectionTab.php:546
msgid ""
"When checked, imported or updated products will have the backorder status "
"set to 'Allow, but notify customer', else I will be set to 'Allow'"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DashboardTab.php:23
msgid "Products & categories synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DashboardTab.php:28
msgid "Orders & customers synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DashboardTab.php:33
msgid "Promotions & coupons synced"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/DashboardTab.php:38
msgid "Payments active"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php:47
msgid "Featured attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php:77
msgid ""
"First set the export mappings below to make sure the export maps are as "
"needed. Please pay attention to this, since there is NO do-overs on \"one "
"time import\". After completing this you can to the \"One Time Export\" "
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php:84
msgid "Not mapped"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportSettingsTab.php:106
msgid "Attributes can only be used once"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:48
msgid "Your file has been generated"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:49
msgid ""
"Your download will start in a few seconds. If not, you can download the file "
"manually using the link below"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:50
msgid ""
"Please wait and keep the page and popup window open while we are preparing "
"your export"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:51
msgid "Preparing export"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:52
msgid "Stop exporting"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:53
msgid "Size"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:54
msgid "Export failed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:55
msgid ""
"Something went wrong during export or server timed out. You can try manual "
"export via command line, do you want to read the guide?"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:56
msgid "No, thanks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:57
msgid "Yes, please"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:76
msgid "Back to export"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:81
msgid "All skus was generated successfully"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:85
msgid "Failed to generate SKU for %s product(s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:117
msgid "Export customers"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:118
msgid "Export tags"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:119
msgid "Export categories"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:120
msgid "Export attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:121
msgid "Export attribute options"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:122
msgid "Export product blueprints"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:123
msgid "Export products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:128
msgid "Download export (csv)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:137
msgid "Go to backoffice import form"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:144
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:176
#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAll.php:35
#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportTag.php:27
msgid "Skip empty tags (tags with no products attached)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:151
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:173
msgid "Include all not active products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:167
msgid "Export full package"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:169
msgid "Download export (zip)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:187
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:148
msgid "See documentation"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:190
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:155
msgid "Check if `wp-cli` is installed in the website's server."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:192
msgid "Open command line and navigate to website directory"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:193
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:197
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:198
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:199
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:200
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:201
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:202
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:203
msgid "Run %s to export %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:193
msgid "full package"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:195
msgid "or alternatively, you can export per file."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:197
msgid "customers"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:198
#: src/StoreKeeper/WooCommerce/B2C/Imports/TagImport.php:195
msgid "tags"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:199
msgid "categories"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:200
msgid "attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:201
#: src/StoreKeeper/WooCommerce/B2C/Imports/AttributeOptionImport.php:112
msgid "attribute options"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:202
msgid "product blueprints"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:203
#: src/StoreKeeper/WooCommerce/B2C/Imports/ProductImport.php:1715
msgid "products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:206
msgid "Each run will return the path of the exported file."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:211
msgid "Manual export from command line using wp-cli"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:215
msgid "Manual export guide"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:239
msgid ""
"With the One Time Export you can export all the data from your WooCommerce "
"webshop to your StoreKeeper BackOffice. After completing this export you "
"should import the files into your StoreKeeper BackOffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:241
msgid "Import & Export Center"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:251
msgid ""
"You should generate all the export files and then go to the \"%1$s\" of this "
"account."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:256
msgid ""
"The correct order of importing is the same as export below, so first "
"customers,tags,categories ect."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:259
msgid ""
"After you complete the full \"One Time Export\" procedure, be aware that "
"from this moment on the management of your webshop goes through the "
"StoreKeeper BackOffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:270
msgid "There are %s product(s) without sku."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:276
msgid "There are %s variations(s) without sku."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:281
msgid ""
"They will not be exported, because they cannot be matched back by sku, which "
"will make duplicates when imported back. If the configurable product does "
"not have sku, it's variations won't be exported as well."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:285
msgid "Generate all missing sku from title"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:297
msgid "Click here to configure them"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:301
msgid ""
"Warning: You didn't set the settings yet for mapping fields, are you really "
"sure?"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:337
msgid "Dutch"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:338
msgid "English"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:339
msgid "German"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:344
msgid "Site language (%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/ExportTab.php:349
msgid "Export language"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:49
msgid "Currently installed theme"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:58
msgid "Validate NL customer address"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:62
msgid ""
"When checked, billing and shipping addresses will be validated on customer's "
"account edit and checkout page when selected country is Netherlands"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:73
msgid "Use image CDN if available"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/FrontendSettingsTab.php:77
msgid ""
"When checked, images will be served using StoreKeeper CDN if available (no "
"product images are stored on the web-shop server that way)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:61
msgid "Total tasks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:66
msgid "Successful tasks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:72
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:107
msgid ""
"\"%s\" only purges items older than 30 days or if there are more than 1000 "
"items, we purge all but the last 1000 items and purge items older than 7 "
"days."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:76
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:83
msgid "Purge successful tasks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:101
msgid "Total webhooks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:111
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:118
msgid "Purge webhooks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:129
msgid "Started purging tasks, please wait."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:142
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:174
msgid "Purged %s items"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:150
msgid "%s tasks has been successfully purged"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:161
msgid "Started purging webhooks, please wait."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/LogPurgerTab.php:182
msgid "%s webhooks has been successfully purged"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/PluginConflictCheckerTab.php:22
msgid "Name"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/PluginConflictCheckerTab.php:26
msgid "Version"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/PluginConflictCheckerTab.php:30
msgid "Conflicts"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:53
msgid "Cron configuration"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:56
msgid "Hook name"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:64
msgid "Last cron execution status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:93
msgid "Advanced configuration"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:131
msgid "Cron runner"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:137
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:426
msgid "Apply"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:139
msgid ""
"Select which cron runner will be used. Please use only if knowledgeable."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:157
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:164
msgid "Add %s to crontab."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:158
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:165
msgid ""
"Upon changing runner, please make sure to remove the cron above from crontab."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:163
msgid "Check if `curl` and `cron` is installed in the website's server."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:170
msgid ""
"You can improve Wordpress Cron performance by using System Task Scheduler. %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:180
msgid "Instructions"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:188
msgid "Statistics"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:200
msgid "Value"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:248
msgid " (Last called %s ago)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:253
msgid "Pre-execution date and time"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:259
msgid "Tasks were processed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:268
msgid "Last processed task date and time"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:275
msgid "Post-execution status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:281
msgid "Post-execution error"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:288
msgid "Running invalid cron in last 5 minutes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SchedulerTab.php:289
msgid "None"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:35
msgid "Setting sku for product variation \"%s\" of product: "
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:45
msgid "Setting sku for product: "
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:55
msgid "Product with id="
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:64
msgid "Sku:"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:67
msgid "Error:"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SkuLogFormatter.php:97
msgid "Product with empty title (id=%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:63
msgid "Registered wordpress hooks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:85
msgid "StoreKeeper options"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:94
msgid "Wp-config settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:103
msgid "Server status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:112
msgid "Database status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:121
msgid "Table foreign keys and InnoDB"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:201
msgid "StoreKeeper database version"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:209
msgid "Database prefix"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:217
msgid "Total database size"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:221
msgid "Data database size"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:225
msgid "Index database size"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:241
msgid "Data: %.2fMB | Index: %.2fMB | Engine: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:266
msgid "The %s table needs to be using Engine=InnoDB in order to use the plugin"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:296
msgid "Foreign key constraint exists"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:299
msgid "Foreign key constraint is missing"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:316
msgid "Set InnoDb  on this table"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:325
msgid "Table is using InnoDB engine"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/StatusTab.php:334
msgid "The %s table was set to Engine=InnoDB."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:85
msgid "Shop info sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:87
msgid "Coupon code sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:89
msgid "Categories sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:91
msgid "Tags/labels sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:93
msgid "Attributes sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:95
msgid "Featured attributes sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:97
msgid "Attribute options sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:99
msgid "Products sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:101
msgid "Upsell product sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:103
msgid "Cross sell product sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:105
msgid "Shipping methods sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:123
msgid ""
"Some synchronizations can take up to 24 hours to complete, leave the page "
"open until its done."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:130
msgid "Sync controls"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:132
msgid "Start sync"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:166
msgid "Executing %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:167
msgid ""
"This can take up to 24 hours depending of your data, please do not close the "
"tab."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:175
msgid "Done executing %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/SynCheckTab.php:180
msgid "%s successful"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:108
msgid "ID"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:112
msgid "Message"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:123
msgid "Log type"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:132
msgid "Times ran"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:136
msgid "Action"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:190
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:419
msgid "Retry"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:191
#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:420
msgid "Mark as success"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:262
msgid "Error key"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:265
msgid "Error reference"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:268
msgid "StoreKeeper plugin version"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:271
msgid "this task was run with older version"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:279
msgid "Extra metadata"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:284
msgid "Shop"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:287
msgid "Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:293
msgid "Stack Trace"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:347
msgid "Apply filter"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:364
msgid "Search"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:365
msgid "Enter message or back reference/post/task ID"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:366
msgid "Go"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:382
msgid "Select log type"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:399
msgid "Select log status"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:416
msgid "Select action"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/TaskLogsTab.php:540
msgid "No extra metadata found."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:20
msgid "Request route"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:24
msgid "Request body"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:28
msgid "Request method"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:32
msgid "Request action"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/Tabs/WebhookLogsTab.php:36
msgid "Response code"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php:17
msgid "Synchronize from StoreKeeper"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php:18
msgid "Plugin conflict checker"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php:19
msgid "One Time Export"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php:20
msgid "One Time Export settings"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Backoffice/Pages/ToolsPage.php:21
msgid "Log purger"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/AbstractCommand.php:77
msgid "EXAMPLES"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/AbstractCommand.php:151
msgid "Syncing from Storekeeper backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/AbstractMarkTasksAs.php:26
msgid "The status of tasks to be marked."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/AbstractMarkTasksAs.php:34
msgid "The type of tasks to be marked."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php:59
msgid "Purge all Storekeeper WooCommerce related entities/objects."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php:64
msgid ""
"Execute this command to delete all tags, coupons, attribute values, "
"attributes, categories, products, orders, tasks and web hook logs."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php:73
msgid "Skips the confirmation whether or not you are sure to continue."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php:79
msgid "Suppress the logs of this command."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/CleanWoocommerceEnvironment.php:85
msgid "Clean products only."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ConnectBackend.php:17
msgid "Generate Storekeeper Backoffice API key."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ConnectBackend.php:26
msgid ""
"Execute this command to generate an API key that will be used to connect "
"synchronization on Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ConnectBackend.php:35
msgid "The base URL of your website."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/AbstractFileExportCommand.php:24
#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportTag.php:19
msgid "The language to which the entities will be exported."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAll.php:14
msgid "Export CSV files for Storekeeper WooCommerce related entities/objects."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAll.php:19
msgid ""
"Generate and export CSV files for customers, tags, categories, attributes, "
"attribute options, product blueprints, and products in a zip file which will "
"be used to import to Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAll.php:28
#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProduct.php:28
msgid "Include products that are not published on export."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttribute.php:14
msgid "Export CSV file for product attributes."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttribute.php:19
msgid ""
"Generate and export CSV files for product attributes which will be used to "
"import to Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttributeOption.php:14
msgid "Export CSV file for product attribute options."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportAttributeOption.php:19
msgid ""
"Generate and export CSV files for product attribute options which will be "
"used to import to Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCategory.php:14
msgid "Export CSV file for product categories."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCategory.php:19
msgid ""
"Generate and export CSV files for product categories which will be used to "
"import to Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCustomer.php:14
msgid "Export CSV file for customers."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportCustomer.php:19
msgid ""
"Generate and export CSV files for customers which will be used to import to "
"Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProduct.php:14
msgid "Export CSV file for products."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProduct.php:19
msgid ""
"Generate and export CSV files for products which will be used to import to "
"Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProductBlueprint.php:14
msgid "Export CSV file for product blueprints."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportProductBlueprint.php:19
msgid ""
"Generate and export CSV files for product blueprints which will be used to "
"import to Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportTag.php:51
msgid "Export CSV file for tags."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/FileExports/FileExportTag.php:56
msgid ""
"Generate and export CSV files for tags which will be used to import to "
"Storekeeper Backoffice."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsRetry.php:13
#: src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsSuccess.php:13
msgid "Mark tasks to \"%s\" status."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsRetry.php:21
#: src/StoreKeeper/WooCommerce/B2C/Commands/MarkTasksAsSuccess.php:21
msgid "Mark selected tasks to \"%s\" status."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelDeleteCommand.php:15
msgid "The ID of the object to be deleted."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/AbstractModelGetCommand.php:15
msgid "The ID of the object to be retrieved."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskDelete.php:13
msgid "Delete task."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskDelete.php:18
msgid "Delete a task by the specified ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskGet.php:13
msgid "Retrieve task."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskGet.php:18
msgid "Retrieve a task by the specified ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskList.php:31
msgid "Retrieve all tasks."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskList.php:36
msgid "Retrieve all tasks from the database."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskList.php:45
msgid "The status of tasks to be retrieved."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurge.php:13
msgid "Purge tasks."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurge.php:18
msgid ""
"Purge successful tasks but only purges items older than 30 days or if there "
"are more than 1000 items, we purge all but the last 1000 items and purge "
"items older than 7 days."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurgeOld.php:13
msgid "Purge old task types."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/Task/TaskPurgeOld.php:18
msgid "Purge all old task types"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogDelete.php:13
msgid "Delete webhook log."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogDelete.php:18
msgid "Delete a webhook log by the specified ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogGet.php:13
msgid "Retrieve webhook log."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogGet.php:18
msgid "Retrieve a webhook log by the specified ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogList.php:13
msgid "Retrieve all webhook log."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogList.php:18
msgid "Retrieve all webhook log from the database."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurge.php:13
msgid "Purge webhook logs."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurge.php:18
msgid ""
"Purge webhook logs but only purges items older than 30 days or if there are "
"more than 1000 items, we purge all but the last 1000 items and purge items "
"older than 7 days."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurgeOld.php:13
msgid "Purge old webhook log types."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ModelCommands/WebhookLog/WebhookLogPurgeOld.php:18
msgid "Purge all old webhook log types"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php:35
msgid "Process all queued synchronization tasks."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php:40
msgid ""
"Process all queued synchronization tasks for products, categories, product "
"stocks, tags, coupon codes, menu items, redirects and orders."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php:49
msgid ""
"Set how many tasks will be processed. Setting limit to 0 means all tasks "
"will be processed."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php:56
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptions.php:34
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProducts.php:35
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProducts.php:34
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProducts.php:35
msgid ""
"Flag to prevent spawning of child processes. Having this might cause "
"timeouts during execution."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessAllTasks.php:62
msgid "It will stop on first failing task"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessSingleTask.php:14
msgid "Process a single task by providing task ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessSingleTask.php:19
msgid "Process a single task at any given status by providing task ID."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ProcessSingleTask.php:28
msgid "The ID of the task to be processed."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/ScheduledProcessor.php:13
msgid ""
" This is used for cron and has cron checks so you should execute wp sk "
"process-all-tasks instead."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php:13
msgid "Sync product attribute options with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php:18
msgid ""
"Sync product attribute options from Storekeeper Backoffice with limit and "
"offset. Note that this should be executed when attributes are already "
"synchronized."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php:27
msgid ""
"Skip other attribute options and synchronize from specified starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php:33
msgid ""
"Determines how many attribute options will be synchronized from the starting "
"point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptionPage.php:39
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:48
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php:44
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:48
msgid "Hide displaying of progress bar while executing command."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptions.php:14
msgid "Sync all product attribute options."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptions.php:19
msgid ""
"Sync all product attribute options from Storekeeper Backoffice and making "
"sure that it is being executed by pages to avoid timeouts. Note that this "
"should be executed when attributes are already synchronized."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributeOptions.php:28
msgid ""
"Specify total amount of attribute options from Storekeeper Backoffice. By "
"default, counts the total amount of attribute options by checking the "
"Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributes.php:12
msgid "Sync all product attributes."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceAttributes.php:17
msgid "Sync all product attributes from Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCategories.php:12
msgid "Sync all tags."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCategories.php:17
msgid "Sync all tags from Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCouponCodes.php:12
msgid "Sync all coupon codes."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCouponCodes.php:17
msgid "Sync all coupon codes from Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:22
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:22
msgid "Sync cross-sell products with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:27
msgid ""
"Sync cross-sell products from Storekeeper Backoffice with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:36
msgid ""
"Skip other cross-sell products and synchronize from specified starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:42
msgid ""
"Determines how many cross-sell products will be synchronized from the "
"starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:94
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:94
#: src/StoreKeeper/WooCommerce/B2C/Imports/AbstractImport.php:255
msgid "Syncing %s from Storekeeper backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProductPage.php:95
msgid "cross-sell products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProducts.php:15
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProducts.php:15
msgid "Sync all cross-sell products."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProducts.php:20
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProducts.php:20
msgid ""
"Sync all cross-sell products from Storekeeper Backoffice and making sure "
"that it is being executed by pages to avoid timeouts."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceCrossSellProducts.php:29
#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProducts.php:29
msgid ""
"Specify total amount of cross-sell products from Storekeeper Backoffice. By "
"default, counts the total amount of products by checking the Storekeeper "
"Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFeaturedAttributes.php:12
msgid "Sync all featured product attribute options."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFeaturedAttributes.php:17
msgid ""
"Sync all featured product attribute options from Storekeeper Backoffice. "
"Note that this should be executed when attributes are already synchronized."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:13
msgid "Sync everything to WooCommerce."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:18
msgid ""
"Sync everything (shop info, categories, tags, attributes, featured "
"attributes, attribute options, products, upsell products, cross-sell "
"products) from Storekeeper Backoffice to WooCommerce."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:19
msgid "SYNCHRONIZATION SEQUENCE"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:41
msgid "Skip synchronization of products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:47
msgid ""
"Skip synchronization of upsell products, this will save a significant amount "
"of time when the user does not have them"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:53
msgid ""
"Skip synchronization of cross-sell products, this will save a significant "
"amount of time when the user does not have them"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:62
msgid "Starting shop information synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:66
msgid "Starting product categories synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:70
msgid "Product categories level (%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:74
msgid "Starting coupon codes synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:78
msgid "Starting tags synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:82
msgid "Starting product attributes synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:86
msgid "Starting featured attributes synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:91
msgid "Starting attribute options synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:104
msgid "Starting products synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:128
msgid "Starting upsell products synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceFullSync.php:134
msgid "Starting cross-sell products synchronization..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php:18
msgid "Sync products with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php:23
msgid "Sync products from Storekeeper Backoffice with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php:32
msgid "Skip other products and synchronize from specified starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProductPage.php:38
msgid ""
"Determines how many products will be synchronized from the starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProducts.php:14
msgid "Sync all products."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProducts.php:19
msgid ""
"Sync all products from Storekeeper Backoffice and making sure that it is "
"being executed by pages to avoid timeouts."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceProducts.php:28
msgid ""
"Specify total amount of products from Storekeeper Backoffice. By default, "
"counts the total amount of products by checking the Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShippingMethods.php:15
msgid "Shipping method synchronization is not enabled."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShippingMethods.php:26
msgid "Sync all shipping methods."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShippingMethods.php:31
msgid ""
"Sync all shipping methods from Storekeeper Backoffice to be used during "
"checkout."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShopInfo.php:14
msgid "Sync shop details."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShopInfo.php:19
msgid ""
"Sync shop details from Storekeeper Backoffice to WooCommerce (address, "
"currency, email, etc.)."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceShopInfo.php:74
msgid "Done synchronizing shop information"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceTags.php:12
msgid "Sync all categories."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceTags.php:17
msgid "Sync all categories from Storekeeper Backoffice"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:27
msgid "Sync upsell products from Storekeeper Backoffice with limit and offset."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:36
msgid ""
"Skip other upsell products and synchronize from specified starting point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:42
msgid ""
"Determines how many upsell products will be synchronized from the starting "
"point."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Commands/SyncWoocommerceUpsellProductPage.php:95
msgid "upsell products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:209
msgid "Every Day"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:256
msgid "%s: You need to have WooCommerce installed for this add-on to work"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:321
msgid ""
"Your WooCommerce version (%s) is lower then the minimum required %s which "
"could cause unexpected behaviour"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:496
msgid "Minimum Cost (%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:558
msgid "Free Shipping"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:587
msgid "Please select an image first."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:588
msgid "Image is too small. Minimum dimensions are {width}x{height}."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:589
msgid "Image is too large. Maximum dimensions are {width}x{height}."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:590
msgid "Image uploaded successfully!"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:591
msgid "An error occurred while uploading the image."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:599
msgid "Nonce verification failed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:610
msgid "Invalid image file. Please upload a valid image."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:617
msgid "Invalid file type. Please upload a valid image."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:622
msgid "File upload failed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Core.php:626
msgid "No file uploaded"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:33
msgid "Every minute"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:86
msgid "WP Cron encountered an error and may not work: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:88
msgid "WP Cron return an unexpected HTTP response code: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:94
msgid "StoreKeeper task processing is not being executed:"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:101
msgid "To see guide on how to configure cron, navigate to %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:102
msgid "Contact your system administrator if the problem persists."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:113
msgid "Wordpress Cron (slowest)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:114
msgid "Crontab with curl API calls"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:115
msgid "Crontab with wp-cli (fastest)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:131
msgid "Task scheduler is set to [%s], change to [%s] to use this."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:140
msgid "Not performed yet."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:142
msgid "Last cron run was successful."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Cron/CronRegistrar.php:144
msgid "Last cron run failed."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Endpoints/WebService/AddressSearchEndpoint.php:25
msgid "Postcode and house number parameter is required."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exceptions/TableOperationSqlException.php:18
msgid "Sql on %s::%s operation: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exceptions/WordpressException.php:17
msgid "Wordpress error message: "
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exports/AbstractExport.php:139
msgid ""
"This channel was disconnected in StoreKeeper Backoffice, please reconnect it "
"manually."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exports/OrderExport.php:144
msgid "Customer Email"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exports/OrderExport.php:146
msgid "Customer phone number"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exports/OrderExport.php:1064
#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:309
msgid "Refund via Wordpress plugin (Refund #%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Exports/OrderExport.php:1179
msgid "Wordpress plugin"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Factories/LoggerFactory.php:33
msgid "Directory \"%s\" was not created"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/FileExport/CustomerFileExport.php:205
msgctxt "full name"
msgid "%1$s %2$s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Filters/OrderTrackingMessage.php:16
msgid ""
"Allows to change the Track&Trace html on the order page before it's shown on "
"the customer order page."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Filters/PrepareProductCategorySummaryFilter.php:17
msgid ""
"Allows to change the product category summary, which is shown below the "
"products in the category archive page."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:46
msgid "Validating postcode and house number. Please wait..."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:47
msgid "Valid postcode and house number"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:48
msgid "Invalid postcode or house number"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:49
msgid "Postcode format for NL address is invalid"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:76
#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:77
msgid "Postcode / ZIP"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:80
msgid "Street address"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:81
msgid "Street name"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:185
#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/AddressFormHandler.php:191
msgid "House number"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/CustomerEmailHandler.php:23
#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/CustomerEmailHandler.php:36
msgid "Please provide a valid email address."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/OrderHookHandler.php:30
msgid ""
"To check your parcel status, go to <a href=\"%s\">Track & Trace page</a>."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php:543
msgid "No %s selected"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php:608
#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php:626
msgid "Enter your required text here..."
msgstr ""

#. translators: 1: product name 2: quantity in stock
#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php:986
msgid ""
"You cannot add that amount of &quot;%1$s&quot; to the cart because there is "
"not enough stock (%2$s remaining)."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/Handlers/ProductAddOnHandler.php:1037
msgid "Emballage fee"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/FormShortCode.php:27
msgid "You are now subscribed."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/FormShortCode.php:29
msgid "You are already subscribed."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Frontend/ShortCodes/FormShortCode.php:32
msgid "An error occurred!"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php:75
msgid "minutes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php:78
msgid "hours"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php:81
msgid "days"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php:84
msgid "months"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/DateTimeHelper.php:86
msgid "years"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:62
msgid "Categories"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:64
msgid "Attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:66
msgid "Attribute options"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:68
msgid "Customers"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:70
msgid "Products"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:72
msgid "Product blueprints"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:74
msgid "Tags"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/FileExportTypeHelper.php:76
msgid "All"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/PluginConflictChecker.php:60
#: src/StoreKeeper/WooCommerce/B2C/Helpers/PluginConflictChecker.php:106
msgid "Not supported"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/PluginConflictChecker.php:107
msgid "Plugin version %s is not compatible with the plugin"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php:44
msgid "Product not found"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php:51
msgid "Product has empty sku"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php:71
msgid "Sku was set for product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php:76
msgid "Failed to set sku"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ProductSkuGenerator.php:86
msgid "Product already has sku set"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/RoleHelper.php:39
msgctxt "User role"
msgid "StoreKeeper Webshop manager"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:32
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:105
msgid "PHP version %s or up"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:37
msgid "PHP version %s found, plugin requires at least version %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:49
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:161
msgid "WooCommerce SKU feature enabled"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:51
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:162
msgid "SKU feature for WooCommerce has been disabled."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:57
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:170
msgid "WooCommerce stock management feature enabled"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:59
#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:171
msgid "WooCommerce stock management feature has been disabled."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:70
msgid "Enabling PHP %s extension is required"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:86
msgid "Enabling PHP %s extension is optional to improve performance"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:109
msgid "Contact your server provider to upgrade your PHP version to at least %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:121
msgid "Writable tmp directory"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:123
msgid ""
"Contact your server provider to allow one of the those directories to be "
"writable: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:146
msgid "Writable log directory"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:148
msgid "Contact your server provider to create the writeable log directory: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:183
msgid ""
"Contact your server provider to enable the PHP %s extension for the "
"StoreKeeper synchronization plugin to function properly"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/ServerStatusChecker.php:199
msgid ""
"Contact your server provider to enable the PHP %s extension to improve the "
"performance and stability"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Helpers/SsoHelper.php:202
#: src/StoreKeeper/WooCommerce/B2C/Helpers/SsoHelper.php:209
#: src/StoreKeeper/WooCommerce/B2C/Helpers/SsoHelper.php:215
#: src/StoreKeeper/WooCommerce/B2C/Helpers/SsoHelper.php:222
msgid "Login expired, please try again"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/AbstractImport.php:395
msgid "Done processing %s items of %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/AttributeImport.php:85
msgid "product attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/CategoryImport.php:396
msgid "product categories"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/CouponCodeImport.php:302
msgid "coupon codes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/FeaturedAttributeImport.php:53
msgid "featured attributes"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/MenuItemImport.php:429
msgid "menu items"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/OrderImport.php:205
msgid "orders"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/ProductImport.php:1617
msgid "Done processing %s items of %s (%s new / %s updated)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/ProductStockImport.php:47
msgid "products stock"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/RedirectImport.php:92
msgid "redirect"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Imports/ShippingMethodImport.php:341
msgid "shipping methods"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Options/CronOptions.php:106
msgid "Successfully processing tasks"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Options/CronOptions.php:108
msgid "Waiting for tasks to process"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Options/CronOptions.php:110
msgid "Processing tasks does not execute correctly"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Options/StoreKeeperOptions.php:107
msgid "Default (%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:102
msgid "The payment has been canceled, please try again"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:107
msgid ""
"Your order is awaiting payment. Once we receive it, we'll process your "
"purchase."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:112
msgid "There was an error during processing of the payment: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:185
msgid "Invalid return url, contact shop owner to check the payment"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/PaymentGateway.php:204
msgid ""
"The order\\'s payment status received: %s\n"
"trx=%s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/StoreKeeperBaseGateway.php:135
msgid "Order number"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/PaymentGateway/StoreKeeperBaseGateway.php:149
msgid ""
"Stating payment %s\n"
" eid: %s, trx: %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tasks/OrderPaymentStatusUpdateTask.php:25
msgid ""
"StoreKeeper: Order was automatically cancelled due to payment expiration "
"(Payment ID=%s)"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:66
msgid "Brand"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:68
msgid "Barcode"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:70
msgid "Printable shortname"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:72
msgid "Needs weight on kassa"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:74
msgid "Needs description on kassa"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:76
msgid "Minimal order quantity"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:78
msgid "In package quantity"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:80
msgid "In box quantity"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:82
msgid "In out quantity"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:84
msgid "Unit weight in grams"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:86
msgid "Season"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:88
msgid "Duration in seconds"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:90
msgid "Condition"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/FeaturedAttributes.php:92
msgid "Sales unit"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/IniHelper.php:49
msgid "Unable to set php configuration option %s"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:134
msgid "New"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:136
msgid "Processing"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:138
msgid "Success"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:140
msgid "Failed"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:230
msgid "Product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:232
msgid "Category"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:234
msgid "Product stock"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:236
msgid "Tag"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:238
msgid "Coupon code"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:240
msgid "Menu item"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:242
msgid "Redirect"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:244
msgid "Order"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:246
#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:316
msgid "Trigger variation save actions"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:248
#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:319
msgid "Parent product recalculation"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:250
msgid "Shipping method"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:252
#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:344
msgid "Report error"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:254
msgid "Other"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:262
msgid "Full import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:265
msgid "Attribute import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:268
msgid "Category import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:271
msgid "Product import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:274
msgid "Product update"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:277
msgid "Product stock update"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:280
msgid "Tag import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:283
msgid "Coupon code import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:286
msgid "Order export"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:289
msgid "Order import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:292
msgid "Order payment status change"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:295
msgid "Delete category"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:298
msgid "Delete product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:301
msgid "Deactiveer product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:304
msgid "Activeer product"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:307
msgid "Delete tag"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:310
msgid "Delete order"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:313
msgid "Delete coupon code"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:322
msgid "Menu item import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:325
msgid "Menu item delete"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:328
msgid "Redirect import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:331
msgid "Redirect delete"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:335
msgid "Shipping method import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:337
msgid "All shipping method import"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/TaskHandler.php:341
msgid "Shipping method delete"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Tools/WordpressExceptionThrower.php:30
msgid "Function returned false"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Updator.php:34
msgid ""
"Table \"%s\" need to be using InnoDB ENGINE. <a href=\"%s\">Go to status "
"page</a> to fix this dependency."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/Updator.php:89
msgid "Failed to update 'StoreKeeper for WooCommerce' plugin to version %s."
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/form-multiple-choice.php:5
#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/form-multiple-choice.php:34
#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/form-single-choice.php:22
msgid "Out of stock"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:17
msgid "Required Image"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:18
msgid "Would you like an image on the product?"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:29
#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/text-addon.php:29
msgid "free"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:32
msgid "Upload Image"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:50
msgid "Select an image"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/image-addon.php:56
msgid "Upload"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/text-addon.php:15
msgid "Required Text"
msgstr ""

#: src/StoreKeeper/WooCommerce/B2C/templates/add-on/text-addon.php:18
msgid "Would you like text on the product?"
msgstr ""

#: storekeeper-woocommerce-b2c.php:55
msgid ""
"%s: Your PHP Version is lower then the minimum required %s, Thus the "
"activation of this plugin will not continue."
msgstr ""

#. Plugin Name of the plugin/theme
msgid "StoreKeeper for WooCommerce"
msgstr ""

#. Description of the plugin/theme
msgid ""
"This plugin provides sync possibilities with the StoreKeeper Backoffice."
msgstr ""

#. Author of the plugin/theme
msgid "Storekeeper"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://www.storekeeper.nl/"
msgstr ""

#. Tags of the plugin/theme
msgid "woocommerce,e-commerce, woo,sales,store"
msgstr ""
