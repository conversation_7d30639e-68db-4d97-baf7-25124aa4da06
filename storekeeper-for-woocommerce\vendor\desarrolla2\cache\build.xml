<?xml version="1.0" encoding="UTF-8"?>

<project name="Cache" default="build">

    <target name="install" depends="composer"
            description="Prepare for execution"/>

    <target name="build" depends="install, init"
            description="Run all tests and build everything"/>

    <target name="metrics" depends="build, phpunit, phpdoc, pdepend, phpcs, phpmd"
            description="Generate Metrics"/>

    <target name="clean"
            description="Cleanup build artifacts">
        <delete dir="cache"/>
        <delete dir="build"/>
    </target>

    <target name="init" depends="clean"
            description="Prepare for build">
        <mkdir dir="cache/htmlpurifier"/>
        <mkdir dir="build/api"/>
        <mkdir dir="build/coverage"/>
        <mkdir dir="build/pdepend"/>
        <mkdir dir="build/phpcs"/>
        <mkdir dir="build/phpmd"/>
    </target>

    <target name="composer"
            description="Composer install">
        <exec executable="composer">
            <arg value="install"/>
        </exec>
    </target>

    <target name="phpdoc"
            description="Generate API documentation using PHPDocumentor">
        <exec executable="phpdoc">
            <arg value="-d"/>
            <arg value="src"/>
            <arg value="-t"/>
            <arg value="build/api/"/>
        </exec>
    </target>

    <target name="phpunit"
            description="Run unit tests using PHPUnit">
        <exec executable="phpunit">
            <arg value="-c"/>
            <arg value="phpunit.xml"/>
        </exec>
    </target>

    <target name="pdepend"
            description="Generate software metrics charts using PHP_Depend">
        <exec executable="pdepend">
            <arg value="--jdepend-chart=build/pdepend/dependencies.svg"/>
            <arg value="--overview-pyramid=build/pdepend/overview-pyramid.svg"/>
            <arg value="src"/>
        </exec>
    </target>

    <target name="phpcs"
            description="Generate coding standard metrics using PHPCS">
        <exec executable="phpcs">
            <arg value="--standard=PSR2"/>
            <arg value="--report-full=build/phpcs/full.txt"/>
            <arg value="--report-summary=build/phpcs/sumary.txt"/>
            <arg value="src"/>
            <arg value="tests"/>
        </exec>
    </target>

    <target name="phpmd"
            description="Generate coding metrics for mess code using PHPMD">
        <exec executable="phpmd">
            <arg value="src"/>
            <arg value="text"/>
            <arg value="codesize,unusedcode,naming,design,controversial"/>
            <arg value="--reportfile"/>
            <arg value="build/phpmd/report.txt"/>
        </exec>

    </target>

    <target name="cs" description="">
        <parallel>
            <exec executable="php-cs-fixer">
                <arg line="fix src"/>
            </exec>
            <exec executable="php-formatter">
                <arg line="formatter:header:fix src"/>
            </exec>
            <exec executable="php-formatter">
                <arg line="formatter:use:sort src"/>
            </exec>
        </parallel>
    </target>
</project>
