<?php

namespace Store<PERSON>eeper\WooCommerce\B2C\Tasks;

use StoreKeeper\WooCommerce\B2C\Imports\AttributeOptionImport;

class AttributeOptionImportTask extends AbstractTask
{
    public function run(array $task_options = []): void
    {
        if ($this->taskMetaExists('storekeeper_id')) {
            $storekeeper_id = $this->getTaskMeta('storekeeper_id');
            $tag = new AttributeOptionImport(
                [
                    'storekeeper_id' => $storekeeper_id,
                    'debug' => key_exists('debug', $task_options) ? $task_options['debug'] : false,
                ]
            );
            $tag->setLogger($this->logger);
            $tag->run();
        }
    }
}
