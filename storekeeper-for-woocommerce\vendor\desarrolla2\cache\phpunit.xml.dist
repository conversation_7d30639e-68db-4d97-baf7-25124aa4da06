<?xml version="1.0" encoding="UTF-8"?>

<phpunit
  bootstrap="./vendor/autoload.php"
  convertWarningsToExceptions="true"
  convertNoticesToExceptions="true"
  convertErrorsToExceptions="true"
  backupStaticAttributes="false"
  processIsolation="false"
  stopOnFailure="false"
  backupGlobals="false"
  colors="true"
>
    <testsuites>
        <testsuite name="Desarrolla2 Cache test suite">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist>
            <directory>./src</directory>
        </whitelist>
    </filter>

    <php>
        <!--ini name="mysqli.default_host" value="localhost" /-->
        <!--ini name="mysqli.default_user" value="root" /-->
        <!--ini name="mysqli.default_pw" value=""/-->
        <const name="CACHE_TESTS_MYSQLI_DATABASE" value="cache_tests" />

        <const name="CACHE_TESTS_MONGO_DSN" value="mongodb://localhost:27017" />
        <const name="CACHE_TESTS_MONGO_DATABASE" value="cache_tests" />

        <const name="CACHE_TESTS_MEMCACHED_SERVER" value="localhost:11211" />

        <const name="CACHE_TESTS_PREDIS_DSN" value="tcp://localhost:6379" />
    </php>
</phpunit>
