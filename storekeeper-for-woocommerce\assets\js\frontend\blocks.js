(()=>{"use strict";const e=window.React,t=window.wp.i18n,n=window.wc.wcBlocksRegistry,i=window.wp.htmlEntities,a=window.wc.wcSettings,s=(0,a.getSetting)("paymentMethodData");Object.keys(s).forEach(s=>{/^sk_pay_id_\d+$/.test(s)&&(s=>{const c=(0,a.getSetting)(`${s}_data`,{}),l=(0,t.sprintf)(/* translators: StoreKeeper Payment %s. */ /* translators: StoreKeeper Payment %s. */
(0,t.__)("StoreKeeper Payment %s","storekeeper-for-woocommerce"),s),o=(0,i.decodeEntities)(c.title)||l,r=(0,i.decodeEntities)(c.icon)||null,d=()=>r?(0,e.createElement)("img",{src:r,style:{float:"right",marginRight:"20px"},alt:o}):"",w=()=>(0,i.decodeEntities)(c.description||""),m={name:s,label:(0,e.createElement)(()=>(0,e.createElement)("span",{style:{width:"100%"}},o,(0,e.createElement)(d,null)),null),content:(0,e.createElement)(w,null),edit:(0,e.createElement)(w,null),canMakePayment:()=>!0,ariaLabel:o,supports:{features:c.supports}};(0,n.registerPaymentMethod)(m)})(s)})})();