body.has-storekeeper-overlay {
    overflow: hidden;
}

body.has-storekeeper-overlay #storekeeper-overlay {
    width: 100vw;
    height: 100vh;
    z-index: 1000000;
    position: absolute;
    top: 0;
    left: 0;


    background: #eaeaea;
    display: flex;
    justify-content: center;
    align-items: center;
}

body.has-storekeeper-overlay #storekeeper-overlay #storekeeper-overlay-modal {
    background: #fff;
    box-shadow: black 0 0 2px 0;

    overflow: auto;

    width: 40vw;
    min-width: 250px;
    min-height: 20vh;
    max-height: 90vh;
}

body.has-storekeeper-overlay #storekeeper-overlay #storekeeper-overlay-modal-wrapper {
    min-height: inherit;

    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

body.has-storekeeper-overlay #storekeeper-overlay #storekeeper-overlay-modal-title {
    font-size: 18px;
    margin: 10px 50px;
}

body.has-storekeeper-overlay #storekeeper-overlay #storekeeper-overlay-modal-content.has-content {
    margin: 10px 50px;
}

